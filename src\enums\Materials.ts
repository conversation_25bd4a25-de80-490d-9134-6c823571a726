import { getText } from "src/i18n";
import { Item } from "src/Interfaces";
import { RARITY, ResourceTypes } from "./common_enum";

export const MATERIALS = {
    "Twig": {
        id: "Twig",
        get description() { return getText("desc_twigs"); },
        "type": ResourceTypes.MATERIAL,
        "icon": "images/branch.png",
        "rarity": RARITY.COMMON,
        "quantityMod": 5,
        "quantityModMap": {
            "BEACH": 1
        },
        "alwaysAvailableBiomes": ["TEMPERATE_RAIN_FOREST", "TEMPERATE_DECIDUOUS_FOREST", "GRASSLAND"],
        "isFlammable": true,
        "burnTime": 15,
    },
    "Leaf": {
        id: "Leaf",
        get name() { return getText("Leaf"); },
        get description() { return getText("desc_leaf"); },
        "type": ResourceTypes.MATERIAL,
        "icon": "images/materials/leaf.png",
        "rarity": RARITY.COMMON,
        "quantityMod": 5,
        "quantityModMap": {
            "BEACH": 1
        },
        "alwaysAvailableBiomes": ["TEMPERATE_RAIN_FOREST", "TEMPERATE_DECIDUOUS_FOREST", "GRASSLAND"],
        "isFlammable": true,
        "burnTime": 10,
    },
    "Mushroom": {
        id: "Mushroom",
        get name() { return getText("Mushroom"); },
        get description() { return getText("desc_mushroom"); },
        "type": ResourceTypes.MATERIAL,
        "icon": "images/mushroom.png",
        "rarity": RARITY.COMMON,
        "quantityMod": 5,
        "alwaysAvailableBiomes": ["TEMPERATE_RAIN_FOREST", "TEMPERATE_DECIDUOUS_FOREST", "GRASSLAND"],
    },
    "Flint": {
        id: "Flint",
        get name() { return getText("Flint"); },
        get description() { return getText("desc_flint"); },
        "type": ResourceTypes.MATERIAL,
        "icon": "images/materials/flint.png",
        "rarity": RARITY.COMMON,
    },
    "Stone": {
        id: "Stone",
        get name() { return getText("Stone"); },
        get description() { return getText("desc_stone"); },
        "type": ResourceTypes.MATERIAL,
        "icon": "images/stone1.png",
        // "icon": "images/rock.svg",
        "rarity": RARITY.COMMON,
        "quantityMod": 3,
    },
    "Log": {
        id: "Log",
        get name() { return getText("Log"); },
        get description() { return getText("desc_log"); },
        "type": ResourceTypes.MATERIAL,
        "icon": "images/wood.png",
        "rarity": RARITY.COMMON,
        "isFlammable": true,
        "burnTime": 30
    },
    "Grass": {
        id: "Grass",
        get name() { return getText("Grass"); },
        get description() { return getText("desc_grass"); },
        "type": ResourceTypes.MATERIAL,
        "icon": "images/materials/grass.png",
        "rarity": RARITY.COMMON,
        "quantityMod": 3,
        "isFlammable": true,
        "burnTime": 10,
    },
    "Bark": {
        id: "Bark",
        get name() { return getText("Bark"); },
        get description() { return getText("desc_bark"); },
        "type": ResourceTypes.MATERIAL,
        "icon": "🌳",
        "rarity": RARITY.COMMON,
        "quantityMod": 3,
        "isFlammable": true,
        "burnTime": 20,
    },
    "Manure": {
        id: "Manure",
        get name() { return getText("Manure"); },
        get description() { return getText("desc_manure"); },
        "type": ResourceTypes.MATERIAL,
        "icon": "images/materials/poop.png",
        "rarity": RARITY.UNCOMMON,
        "quantityMod": 3,
    },
    "Sand": {
        "id": "Sand",
        get name() { return getText("Sand"); },
        get description() { return getText("desc_sand"); },
        "type": ResourceTypes.MATERIAL,
        "icon": "images/sand.png",
        "rarity": RARITY.COMMON,
        "quantityMod": 3,
    },
    "FishBone": {
        id: "FishBone",
        get name() { return getText("Fish Bone"); },
        get description() { return getText("desc_fish_bone"); },
        "type": ResourceTypes.MATERIAL,
        "icon": "🦴",
        "rarity": RARITY.COMMON,
        "quantityMod": 3,
    },
    "Salt": {
        id: "Salt",
        get name() { return getText("Salt"); },
        get description() { return getText("desc_salt"); },
        "type": ResourceTypes.MATERIAL,
        "icon": "🧂",
        "rarity": RARITY.UNCOMMON,
        "quantityMod": 3,
    },
    "Bamboo": {
        id: "Bamboo",
        get name() { return getText("Bamboo"); },
        get description() { return getText("desc_bamboo"); },
        "type": ResourceTypes.MATERIAL,
        "icon": "🎋",
        "rarity": RARITY.EPIC,
        "quantityMod": 2,
    },
    "Coral": {
        id: "Coral",
        get name() { return getText("Coral"); },
        get description() { return getText("desc_coral"); },
        "type": ResourceTypes.MATERIAL,
        "icon": "🪸",
        "rarity": RARITY.EPIC,
        "quantityMod": 3,
    },
    "Pearl": {
        id: "Pearl",
        get name() { return getText("Pearl"); },
        get description() { return getText("desc_pearl"); },
        "type": ResourceTypes.MATERIAL,
        "icon": "💎",
        "rarity": RARITY.LEGENDARY,
        "quantityMod": 1,
    },
    "Rawhide": {
        id: "Rawhide",
        get name() { return getText("Rawhide"); },
        get description() { return getText("desc_rawhide"); },
        "type": ResourceTypes.MATERIAL,
        "icon": "images/rawhide.svg",
        "rarity": RARITY.COMMON
    },
    "Fiber": {
        id: "Fiber",
        get name() { return getText("Fiber"); },
        get description() { return getText("desc_Fiber"); },
        "type": ResourceTypes.MATERIAL,
        "icon": "images/Fiber.svg",
        "rarity": RARITY.COMMON
    }
};
