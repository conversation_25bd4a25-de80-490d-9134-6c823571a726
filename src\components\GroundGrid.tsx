
import React, { useState, useCallback } from 'react';
import { MacOSModal } from './WindowManagement/MacOSModal';
import { PlacedBuilding, InventoryItemStack, StorageBuilding } from 'src/Interfaces';
import { getText } from 'src/i18n';
import { useRootStore } from 'src/stores/rootStore';
import { ItemsGrid } from './Inventory/ItemsGrid';
import { StorageGrid } from './Storage/StorageGrid';
import {
  DndContext,
  useSensor,
  useSensors,
  MouseSensor,
  TouchSensor,
  DragStartEvent,
  DragEndEvent,
  DragOverlay,
  rectIntersection,
  CollisionDetection,
} from "@dnd-kit/core";
import { snapCenterToCursor } from '@dnd-kit/modifiers';
import { InventoryItem } from './Inventory/InventoryItem';
import { Items } from 'src/enums/resources';
import { InventoryItemSlot } from './Inventory/InventoryItemSlot';
import { useGroundStore } from 'src/stores/groundStore';
import { StorageDragDropWrapper } from './Storage/StorageDragDropWrapper';
import { InventoryItemContent } from './Inventory/InventoryItemContent';

// Fix collision detection for proper drag highlighting
const fixCursorSnapOffset: CollisionDetection = (args) => {
  // Bail out if keyboard activated
  if (!args.pointerCoordinates) {
    return rectIntersection(args);
  }
  const { x, y } = args.pointerCoordinates;
  const { width, height } = args.collisionRect;
  const updated = {
    ...args,
    // The collision rectangle is broken when using snapCenterToCursor. Reset
    // the collision rectangle based on pointer location and overlay size.
    collisionRect: {
      width,
      height,
      bottom: y + height / 2,
      left: x - width / 2,
      right: x + width / 2,
      top: y - height / 2,
    },
  };
  return rectIntersection(updated);
};

export const GroundGrid = () => {
  const [activeId, setActiveId] = useState<string | null>(null);
  const [selectedInventoryStack, setSelectedInventoryStack] = useState<InventoryItemStack | null>(null);
  const [selectedGroundStorageStack, setSelectedGroundStorageStack] = useState<InventoryItemStack | null>(null);

  // Get inventory and storage data from stores
  const itemStacks = useRootStore(state => state.itemStacks);
  const setItemStacks = useRootStore(state => state.setItemStacks);
  // const placedBuildingsInAllRegions = useRootStore(state => state.placedBuildingsInAllRegions);
  const currRegionIndex = useRootStore(state => state.currRegionIndex);
  const groundStorageStacks = useGroundStore(state => state.allRegionGroundStacks[currRegionIndex]);
  const allRegionGroundStacks = useGroundStore(state => state.allRegionGroundStacks);
  const addToGroundStacks = useGroundStore(state => state.addToGroundStacks);
  const removeGroundStackByIndex = useGroundStore(state => state.removeStackByIndex);
  const swapGroundStorageStacks = useGroundStore(state => state.swapStacks);
  const swapInventoryStacks = useRootStore(state => state.swapStacks);
  const addStackByIndex = useGroundStore(state => state.addStackByIndex);


  if (!itemStacks) {
    return null;
  } else if (!groundStorageStacks) {
    return null;
  }
  console.log("groundStorageStacks", groundStorageStacks);
  console.log("activeId", activeId);


  // Drag and drop sensors
  const sensors = useSensors(
    useSensor(MouseSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(TouchSensor, {
      activationConstraint: {
        delay: 250,
        tolerance: 5,
      },
    })
  );

  const handleDragStart = useCallback((event: DragStartEvent) => {
    setActiveId(event.active.id as string);
  }, []);

  const handleDragEnd = useCallback((event: DragEndEvent) => {
    const { active, over } = event;
    setActiveId(null);

    if (!over) return;

    const activeId = active.id as string;
    const overId = over.id as string;

    console.log('Drag end:', { activeId, overId });

    // Parse IDs to determine source and destination
    const isActiveFromInventory = activeId.startsWith('draggable-');
    const isActiveFromStorage = activeId.startsWith('storage-draggable-');
    const isOverInventory = overId.startsWith('droppable-');
    const isOverStorage = overId.startsWith('storage-droppable-');

    console.log('Drag classification:', {
      isActiveFromInventory,
      isActiveFromStorage,
      isOverInventory,
      isOverStorage
    });

    if (isActiveFromInventory && isOverStorage) {
      // Moving from inventory to storage
      const inventoryIndex = parseInt(activeId.replace('draggable-', ''));
      const storageIndex = parseInt(overId.replace('storage-droppable-', ''));
      
      const itemStackToMove = itemStacks[inventoryIndex];
      console.log('Moving item to storage:', { inventoryIndex, storageIndex, itemStackToMove });

      if (itemStackToMove) {
        // Remove from inventory
        const newItemStacks = [...itemStacks];
        newItemStacks[inventoryIndex] = null;
        setItemStacks(newItemStacks);

        // Add to storage - maintain slot positions
        addStackByIndex(currRegionIndex, storageIndex, itemStackToMove);

      }
    } else if (isActiveFromStorage && isOverInventory) {
      // Moving from storage to inventory
      const storageIndex = parseInt(activeId.replace('storage-draggable-', ''));
      const inventoryIndex = parseInt(overId.replace('droppable-', ''));

      const storageStack = groundStorageStacks[storageIndex];
      if (storageStack) {
        // Add to inventory
        const newItemStacks = [...itemStacks];
        newItemStacks[inventoryIndex] = storageStack;
        console.log('222 Moving item to inventory:', { storageIndex, inventoryIndex, newItemStacks });
        setItemStacks(newItemStacks);

        // Remove from storage - find and remove the item at the specific slot
        removeGroundStackByIndex(currRegionIndex, storageIndex);
      }
    } else if (isActiveFromStorage && isOverStorage) {
      const fromIndex = parseInt(activeId.replace('storage-draggable-', ''));
      const toIndex = parseInt(overId.replace('storage-droppable-', ''));

      swapGroundStorageStacks(currRegionIndex, fromIndex, toIndex);
    } else if (isActiveFromInventory && isOverInventory) {
      const fromIndex = parseInt(activeId.replace('draggable-', ''));
      const toIndex = parseInt(overId.replace('droppable-', ''));

      swapInventoryStacks(fromIndex, toIndex);
    }
  }, [itemStacks, groundStorageStacks, setItemStacks, addToGroundStacks, removeGroundStackByIndex, swapGroundStorageStacks, swapInventoryStacks]);

  // Get the currently dragged item for the overlay
  const activeItem = activeId ? (() => {
    if (activeId.startsWith('draggable-')) {
      const index = parseInt(activeId.replace('draggable-', ''));
      return itemStacks[index];
    } else if (activeId.startsWith('storage-draggable-')) {
      const index = parseInt(activeId.replace('storage-draggable-', ''));
      return groundStorageStacks[index];
    }
    return null;
  })() : null;

  const activeItemDef = activeItem ? Items[activeItem.itemId] : null;

  return (
      <DndContext
        sensors={sensors}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
        collisionDetection={fixCursorSnapOffset}
      >
        <div className="storage-modal">
          {/* Player Inventory Section */}
          <div className="storage-section">
            <h3>{getText('Inventory')}</h3>
            <ItemsGrid
              itemStacks={itemStacks}
              selectedStack={selectedInventoryStack}
              setSelectedStack={setSelectedInventoryStack}
              activeId={activeId}
            />
          </div>

          {/* Storage Section */}
          <div className="storage-section">
            <h3>{`${getText('Storage')} `}</h3>
            <GroundStorageGrid
              itemStacks={groundStorageStacks}
              selectedStack={selectedGroundStorageStack}
              setSelectedStack={setSelectedGroundStorageStack}
              activeId={activeId}
            />
          </div>

          {/* Drag Overlay */}
          <DragOverlay modifiers={[snapCenterToCursor]} dropAnimation={null}>
            {activeItem && activeItemDef ? (
              <div className="drag-overlay-item">
                <InventoryItem itemStack={activeItem} itemDef={activeItemDef} />
              </div>
            ) : null}
          </DragOverlay>
        </div>
      </DndContext>
  );
};



interface GroundStorageGridProps {
    itemStacks: InventoryItemStack[];
    selectedStack: InventoryItemStack | null;
    setSelectedStack: (itemStack: InventoryItemStack) => void;
    activeId: string | null;
}

export const GroundStorageGrid = React.memo(({
    itemStacks,
    selectedStack,
    setSelectedStack,
    activeId
}: GroundStorageGridProps) => {

    if (!itemStacks) {
        return null;
    }

    return (
        <div className="inventory-grid">
            {new Array(itemStacks.length + 5).fill(null).map((_, index) => (
                <InventoryItemSlot
                    key={index}
                    itemStack={itemStacks[index]}
                    stackIndex={index}
                    isSelected={itemStacks[index] && selectedStack?.uuid === itemStacks[index].uuid}
                    setSelectedStack={setSelectedStack}
                    isActive={`draggable-${index}` === activeId}
                />
            ))}
        </div>
    );
});


export const InventoryItemSlot = React.memo((props: {
    itemStack: InventoryItemStack,
    setSelectedStack: (itemStack: InventoryItemStack, element?: HTMLElement) => void,
    stackIndex: number,
    isActive: boolean,
    isSelected: boolean,
}) => {
    console.log("InventoryItemSlot rendered!!!!", props.stackIndex, props.isActive);

    // Create the data object
    const draggableData = {
        index: props.stackIndex,
        itemStack: props.itemStack
    };

    // Use the DragDropWrapper to isolate the DnD context changes
    // Pass isSelected directly to DragDropWrapper so it will re-render when selection changes
    return (
        <StorageDragDropWrapper
            id={`${props.stackIndex}`}
            data={draggableData}
            disabled={!props.itemStack}
            isSelected={props.isSelected}
            // setSelectedStack={props.setSelectedStack}
        >
            {({ ref, isDragging, isOver, attributes, listeners, isSelected }) => (
                <InventoryItemContent
                    itemStack={props.itemStack}
                    isSelected={isSelected}
                    setSelectedStack={props.setSelectedStack}
                    isDragging={isDragging}
                    isOver={isOver}
                    attributes={attributes}
                    listeners={listeners}
                    innerRef={ref}
                />
            )}
        </StorageDragDropWrapper>
    );
}, (prevProps, nextProps) => {
    // Custom equality function to prevent unnecessary re-renders
    if (prevProps.isActive !== nextProps.isActive) return false;
    if (prevProps.isSelected !== nextProps.isSelected) {
        console.log("selected changed 000", nextProps.stackIndex, prevProps.isSelected, nextProps.isSelected);
        return false;
    }

    // Check if itemStack has changed
    if (!prevProps.itemStack && !nextProps.itemStack) return true;
    if (!prevProps.itemStack || !nextProps.itemStack) return false;

    return (
        prevProps.itemStack.uuid === nextProps.itemStack.uuid &&
        prevProps.itemStack.quantity === nextProps.itemStack.quantity
    );
});




// import {
//   DndContext,
//   useSensor,
//   useSensors,
//   MouseSensor,
//   TouchSensor,
//   DragOverlay,
//   DragStartEvent,
//   DragEndEvent,
//   CollisionDetection,
//   rectIntersection,
// } from "@dnd-kit/core";
// import React from "react";
// import { InventoryItemStack } from "src/Interfaces";
// import { useGroundStore } from "src/stores/groundStore";
// import { DragDropWrapper } from "./DragDropWrapper";
// import { ItemDescriptionPopover } from "./ItemDescriptionPopover";



// export const GroundGrid = () => {
//     const itemStacks = useGroundStore(state => state.groundStacks);
//     const setItemStacks = useGroundStore(state => state.setGroundStacks);

//     const [selectedStack, setSelectedStack] = React.useState<InventoryItemStack>(null);
//     const [activeId, setActiveId] = React.useState(null);
//     const [activeIndex, setActiveIndex] = React.useState(null);
//     const [selectedItemRect, setSelectedItemRect] = React.useState<DOMRect | null>(null);


//     // Function to handle item selection/deselection
//     const handleItemSelection = React.useCallback((itemStack: InventoryItemStack, element?: HTMLElement) => {
//         // Store the bounding rectangle of the selected item for popover positioning
//         if (element) {
//             // Get the bounding rectangle relative to the viewport
//             const rect = element.getBoundingClientRect();
//             console.log("Selected item rect:", rect);

//             // Set the rect first, then the selected stack to ensure the popover has the position
//             // before it tries to render
//             setSelectedItemRect(rect);

//             // Use a small timeout to ensure the rect is set before the popover renders
//             setTimeout(() => {
//                 setSelectedStack(itemStack);
//             }, 0);
//         } else {
//             console.warn("No element reference provided for popover positioning");
//             setSelectedStack(itemStack);
//         }
//     }, [selectedStack]);


//     // Configure sensors for drag detection
//     // Don't use useMemo for these as they're causing dependency array issues
//     const mouseSensor = useSensor(MouseSensor, {
//         // Require the mouse to move by 10 pixels before activating
//         activationConstraint: {
//             distance: 10,
//         },
//     });

//     const touchSensor = useSensor(TouchSensor, {
//         // Press delay of 250ms, with tolerance of 5px of movement
//         activationConstraint: {
//             delay: 250,
//             tolerance: 5,
//         },
//     });

//     // Create sensors without memoization to avoid dependency array issues
//     const sensors = useSensors(mouseSensor, touchSensor);

//     // Memoize event handlers to prevent re-renders
//     const handleDragStart = React.useCallback((event: DragStartEvent) => {
//         const { active } = event;
//         const { index } = active.data.current;
//         setActiveId(active.id);
//         setActiveIndex(index);
//     }, []);

//     const handleDragEnd = React.useCallback((event: DragEndEvent) => {
//         const { active, over } = event;

//         if (over && active.id !== over.id) {
//             const fromIndex = active.data.current.index;
//             const toIndex = over.data.current.index;


//             // Swap the items
//             const newItemStacks = [...itemStacks];
//             const temp = newItemStacks[fromIndex];
//             newItemStacks[fromIndex] = newItemStacks[toIndex];
//             newItemStacks[toIndex] = temp;
//             console.log("Swapping", fromIndex, "to", toIndex, itemStacks, newItemStacks);
//             setItemStacks(newItemStacks);
//         }

//         setActiveId(null);
//         setActiveIndex(null);
//     }, [itemStacks, setItemStacks, setActiveId, setActiveIndex]);



//     return (
//         <DndContext
//             sensors={sensors}
//             onDragStart={handleDragStart}
//             onDragEnd={handleDragEnd}
//             collisionDetection={fixCursorSnapOffset}
//         >
//             <div id="inventoryContent">
//                 {/* <h3 className="inventory-container">{getText("Inventory")}:</h3>
//                 <div className="inventory-count">
//                     {itemStacks.length}/{props.inventorySize} slots
//                 </div> */}
//                 <GroundGridInner
//                     itemStacks={itemStacks}
//                     selectedStack={selectedStack}
//                     setSelectedStack={handleItemSelection}
//                     activeId={activeId}
//                 />

//                 {/* Render the popover when an item is selected */}
//                 {selectedStack && (
//                     <ItemDescriptionPopover
//                         selectedStack={selectedStack}
//                         // itemDef={itemDef}
//                         anchorRect={selectedItemRect}
//                         isOpen={!!selectedStack}
//                         onClose={() => {
//                             setSelectedStack(null);
//                             setSelectedItemRect(null);
//                         }}
//                     />
//                 )}
//             </div>

//             <DragOverlay modifiers={[snapCenterToCursor]} dropAnimation={null} >
//                 {activeItem && activeItemDef && (
//                     <div className="inventory-item-overlay">
//                         {/* <div className="inventory-item-inner"> */}
//                         <div className="drag-overlay">
//                             <InventoryItem
//                                 itemStack={activeItem}
//                                 itemDef={activeItemDef}
//                             />
//                         </div>
//                     </div>
//                 )}
//             </DragOverlay>
//         </DndContext>
//     )
// };


// interface GroundGridProps {
//     itemStacks: InventoryItemStack[];
//     selectedStack: InventoryItemStack | null;
//     setSelectedStack: (itemStack: InventoryItemStack) => void;
//     activeId: string | null;
// }

// export const GroundGridInner = React.memo(({
//     itemStacks,
//     selectedStack,
//     setSelectedStack,
//     activeId
// }: GroundGridProps) => {

//     // console.log("selectedStack ", selectedStack?.uuid === itemStacks[9]?.uuid);

//     return (
//         <div className="inventory-grid">
//             {new Array(DEFAULT_INV_SLOT + (equippedItems.BACKPACK?.itemDef.storageCapacity ?? 0)).fill(null).map((_, index) => (
//                 <GroundItemSlot
//                     key={index}
//                     stackIndex={index}
//                     isSelected={itemStacks[index] && selectedStack?.uuid === itemStacks[index].uuid}
//                     setSelectedStack={setSelectedStack}
//                     isActive={`draggable-${index}` === activeId}
//                 />
//             ))}
//         </div>
//     );
// });


// export const GroundItemSlot = (props: {
//     setSelectedStack: (itemStack: InventoryItemStack, element?: HTMLElement) => void,
//     stackIndex: number,
//     isActive: boolean,
//     isSelected: boolean,
// }) => {
//     console.log("InventoryItemSlot rendered!!!!", props.stackIndex, props.isActive);

//     // TODO: directly get item from ground store
//     const itemStack = useGroundStore(state => state.groundStacks[props.stackIndex]);

//     // Create the data object
//     const draggableData = {
//         index: props.stackIndex,
//         itemStack: itemStack
//     };

//     // Use the DragDropWrapper to isolate the DnD context changes
//     // Pass isSelected directly to DragDropWrapper so it will re-render when selection changes

//     // TODO: do not use this DragDropWrapper
//     return (
//         <DragDropWrapper
//             id={`${props.stackIndex}`}
//             data={draggableData}
//             disabled={!itemStack}
//             isSelected={props.isSelected}
//             // setSelectedStack={props.setSelectedStack}
//         >
//             {({ isDragging, isOver, attributes, listeners, isSelected }) => (
//                 <GroundItemContent
//                     itemStack={itemStack}
//                     isSelected={isSelected}
//                     setSelectedStack={props.setSelectedStack}
//                     isDragging={isDragging}
//                     isOver={isOver}
//                     attributes={attributes}
//                     listeners={listeners}
//                 />
//             )}
//         </DragDropWrapper>
//     );
// };



// // Props for the InventoryItemContent component
// interface InventoryItemContentProps {
//     itemStack: InventoryItemStack;
//     isSelected: boolean;
//     setSelectedStack: (itemStack: InventoryItemStack, element?: HTMLElement) => void;
//     isDragging: boolean;
//     isOver: boolean;
//     attributes?: any;
//     listeners?: any;
//     innerRef?: React.Ref<HTMLDivElement>;
// }

// // Create a component that handles only the rendering of the item
// export const GroundItemContent = (props: InventoryItemContentProps) => {

//     console.log("slot content rendered!!!!", props.isSelected);

//     const {
//         itemStack,
//         isSelected,
//         setSelectedStack,
//         isDragging,
//         isOver,
//         attributes,
//         listeners,
//         innerRef
//     } = props;

//     if (!itemStack) {
//         return (
//             <div
//                 ref={innerRef as React.RefObject<HTMLDivElement>}
//                 className={`inventory-item empty ${isOver ? 'drag-over' : ''}`}
//             >
//                 <div className="inventory-item-inner">
//                 </div>
//             </div>
//         );
//     }

//     const itemDef = Items[itemStack.itemId];

//     if (!itemDef) {
//         console.error("Item definition not found for itemStack", itemStack);
//     }

//     return (
//         <>
//             <div
//                 ref={innerRef as React.RefObject<HTMLDivElement>}
//                 className={`inventory-item${isOver ? ' drag-over' : ''}${isSelected ? ' selected' : ''}`}
//             >
//                 <div
//                     className="inventory-item-inner"
//                     {...attributes}
//                     {...listeners}
//                     onClick={(e) => {
//                         // Use the parent element (inventory-item) as the anchor for the popover
//                         // This gives us the full item cell as the reference point
//                         const itemElement = e.currentTarget.closest('.inventory-item');
//                         console.log("Selected item element:", itemElement);
//                         setSelectedStack(itemStack, itemElement as HTMLElement);

//                         // Prevent event bubbling
//                         e.stopPropagation();
//                     }}
//                 >
//                     {!isDragging && <InventoryItem itemStack={itemStack} itemDef={itemDef} />}
//                 </div>
//             </div>
//         </>
//     );
// };

