/* // @use "battle";
// @use "inventory";
// @use "recipe";
// @use "terrainResources";
// @use "equipment"; */

@import url('../build/css/sassEntrance.css');

@import url('_battle.css');
@import url('_equipment.css');
@import url('_recipe.css');
@import url('_terrainResources.css');
@import url('_building.css');
/* @import url('_building3d.css'); */
@import url('_drag_overlay.css');
@import url('_tabs.css');
/* @import url('../build/js/_bundle.css'); */
@import url('ReactCrafting.css');
@import url('WindowManagement/MainWindowManagementStyles.css');
@import url('cooking.css');
@import url('fishing-modals.css');
@import url('time-display.css');
@import url('boat-system.css');
@import url('storage.css');

/* @import url('_building3d_new.css'); */
/* @import url('_building_switcher.css'); */

/* @import "tailwindcss"; */

/* @import "tw-animate-css"; */

/* @custom-variant dark (&:is(.dark *)); */

html {
    /* cursor: url('cursor/cursor2.svg') 6 6, default; */
    cursor: url('cursor/cursor0.svg') 9 9, default;
    /* cursor: url('cursor/cursor_radix.svg') 20 20, default; */
    /* cursor: url('cursor/pointer_b_shaded.svg') 9 9, default; */
    /* cursor: url('cursor/cursor3.png'), default; */

    -webkit-touch-callout: none; /* iOS Safari */
    -webkit-user-select: none; /* Safari */
     -khtml-user-select: none; /* Konqueror HTML */
       -moz-user-select: none; /* Firefox */
        -ms-user-select: none; /* Internet Explorer/Edge */
            user-select: none; /* Non-prefixed version, currently
                                  supported by Chrome and Opera */
}

.green-btn {
    background-color: rgba(80, 120, 40, 0.8);
    
    &:hover {
        background-color: rgba(100, 150, 50, 0.9);
    }
}

#groundWrapper {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;
    height: 100%;
    width: 100%;
}

.inventory-item {
    /* cursor: url('cursor/small_pointer.svg') 9 9, grab; */
    user-select: none;
    transition: transform 0.2s, opacity 0.2s;
    position: relative;
    aspect-ratio: 1;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 5px;
}

/* a, button {
    cursor: url('../images/pointer_a.svg'), pointer;
} */

.grab-cursor {
    cursor: url('../images/pointer_a.svg'), grab;
}

.hidden {
    display: none;
}

#bottom-overlay {
    position: absolute;
    pointer-events: auto; /* Ensure bottom-overlay can receive right-clicks */
    z-index: 5; /* Make sure it's above other elements but below windows */
}

#react-root {
    height: 90%;
    width: 100%;
    margin-right: auto;
    margin-left: auto;
    pointer-events: none; /* Allow right-clicks to pass through to bottom-overlay */
}
.radix-themes:where([data-is-root-theme='true']) {
    /* position: static; */
    /* z-index: 0; */
    /* min-height: 100vh; */
    background-color: rgb(0 0 0 / 80%);
    pointer-events: none; /* Allow right-clicks to pass through */
  }

.unicode-icon {
    font-size: 24px;
}

.icon-img-div {
    width: 90%;
    height: 90%;
    display: flex;
    overflow: hidden;
    align-items: center;
    justify-content: center;
    max-width: 30px;
    height: 30px;
}

.icon-img {
    /* width: 30px; */
    /* height: 30px; */
    width: 100%;
    height: 100%;
    object-fit: cover;
    /* mask-image: linear-gradient(to bottom, black, transparent); */
    /* mix-blend-mode: multiply; */
    filter: contrast(1.2) brightness(0.8);
    /* filter: invert(1) hue-rotate(180deg) brightness(0.7) invert(1); */
}

.prompt-overlay {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 15px 20px;
    border-radius: 5px;
    z-index: 2000;
    animation: fadeOut 3s forwards;
    font-weight: bold;
}


/* .scrollable-container::-webkit-scrollbar {
    width: 5px;
    transition: all 0.3s ease;
}

.scrollable-container:hover::-webkit-scrollbar {
    width: 5px;
}

.scrollable-container::-webkit-scrollbar-thumb {
    border-radius: 30px;
    transition: all 0.3s;
}

.scrollable-container:hover::-webkit-scrollbar-thumb {
    background: #4a90e2;
} */


.scrollable-container {
    overflow-y: auto;
    overflow-x: hidden;
    &::-webkit-scrollbar-thumb:hover {
        background-color: rgba(255, 255, 255, 0.5);
      /* background: #a8a8a8 !important; */
    }
    &::-webkit-scrollbar-track {
      -webkit-box-shadow: inset 0 0 6px rgba(83, 83, 83, 0.07);
      -webkit-border-radius: 10px;
      background: rgba(0, 0, 0, 0.2);
      border-radius: 4px;
    }
    &::-webkit-scrollbar {
      width: 4px;
      transition: all 0.3s ease;
      /* background-color: #f1f1f1; */
    }
    &::-webkit-scrollbar-thumb {
        background-color: rgba(255, 255, 255, 0.3);
        border-radius: 4px;
        transition: all 0.3s;
    }
}

* {
    box-sizing: border-box;
}

.transparent-window.hide {
    display: none;
}

/* html,
  body {
    margin: 0;
    padding: 0;
  } */

:root {
    --pixel-size: 2px;
    --grid-cell: calc(var(--pixel-size) * 16);
    --bg: #bc4dff;
    --radius: 0.625rem;
    --background: oklch(1 0 0);
    --foreground: oklch(0.13 0.028 261.692);
    --card: oklch(1 0 0);
    --card-foreground: oklch(0.13 0.028 261.692);
    --popover: oklch(1 0 0);
    --popover-foreground: oklch(0.13 0.028 261.692);
    --primary: oklch(0.21 0.034 264.665);
    --primary-foreground: oklch(0.985 0.002 247.839);
    --secondary: oklch(0.967 0.003 264.542);
    --secondary-foreground: oklch(0.21 0.034 264.665);
    --muted: oklch(0.967 0.003 264.542);
    --muted-foreground: oklch(0.551 0.027 264.364);
    --accent: oklch(0.967 0.003 264.542);
    --accent-foreground: oklch(0.21 0.034 264.665);
    --destructive: oklch(0.577 0.245 27.325);
    --border: oklch(0.928 0.006 264.531);
    --input: oklch(0.928 0.006 264.531);
    --ring: oklch(0.707 0.022 261.325);
    --chart-1: oklch(0.646 0.222 41.116);
    --chart-2: oklch(0.6 0.118 184.704);
    /* --chart-3: oklch(0.398 0.07 227.392); */
    --chart-4: oklch(0.828 0.189 84.429);
    --chart-5: oklch(0.769 0.188 70.08);
    --sidebar: oklch(0.985 0.002 247.839);
    --sidebar-foreground: oklch(0.13 0.028 261.692);
    --sidebar-primary: oklch(0.21 0.034 264.665);
    --sidebar-primary-foreground: oklch(0.985 0.002 247.839);
    --sidebar-accent: oklch(0.967 0.003 264.542);
    --sidebar-accent-foreground: oklch(0.21 0.034 264.665);
    --sidebar-border: oklch(0.928 0.006 264.531);
    --sidebar-ring: oklch(0.707 0.022 261.325);
}

/* @media(min-width: 700px) {
    :root {
      --pixel-size: 3px;
    }
  }

  @media(min-width: 1000px) {
    :root {
      --pixel-size: 4px;
    }
  } */

html,
body {
    height: 100%;
    overflow: hidden;
}

body {
    font-family: "Open Sans", "Segoe UI", sans-serif;
    font-size: 14px;
    background-color: hsl(60, 10%, 80%);
    border: 1px solid hsl(60, 20%, 60%);
    display: flex;
    flex-direction: column;
    height: 100vh;
    width: 100vw;
    overflow: hidden;
    color:hsl(0, 0%, 64%);
}

main {
    flex: auto;
    display: flex;
    flex-direction: row;
}

footer {
    flex: initial;
    height: 0;
}

#ui {
    display: flex;
    flex-direction: column;
    flex: initial;
    justify-content: space-between;
    align-items: center;
    padding: 0.5em;
    width: 200px;
}

label {
    white-space: nowrap;
}

.row {
    flex: initial;
    justify-content: space-evenly;
}

.row>* {
    display: block;
}

#group-sliders label,
#group-region-count label {
    font-size: 80%;
}

#group-sliders>label>span:first-child {
    display: inline-block;
    width: 3rem;
    text-align: right;
}

/* #output {
    background-color: #44447a;
    border-right: 1px solid hsl(60, 20%, 60%);
    border-bottom: 1px solid hsl(60, 20%, 60%);
    display: flex;
    flex: auto;
    justify-content: space-evenly;
    position: relative;
  } */

#map {
    background-color: hsla(240, 20%, 90%, 0.5);
    /* width: 1000px;
    height: 1000px; */
    /* image-rendering: pixelated; */
    /* background-image: url("https://assets.codepen.io/21542/CameraDemoMap.png"); */
    /* background-size: 100%;
    width: calc(13 * var(--grid-cell));
    height: calc(10 * var(--grid-cell));
    position: relative; */
}

input[type='range'] {
    width: 80px;
    cursor: ew-resize;
}

.clickable-labels label {
    cursor: pointer;
}

label:hover {
    background: rgba(255, 255, 255, 0.1);
}

#window-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    gap: 10px;
    width: 60%;
    height: 85%;
}

.transparent-window {
    background-color: rgb(0 0 0 / 90%);
    color: white;
    padding: 20px;
    border-radius: 3px;
    display: flex;
    width: 100%;
    height: 100%;
    justify-content: space-around;
    align-items: stretch;
    flex-direction: row;
}

.button-container {
    transform: translateX(-50%);
    height: 6%;
    margin-left: auto;
    margin-right: auto;
    display: flex;
    justify-content: center;
    position: absolute;
    top: 93.2%;
    left: 50%;
    /* padding: 20px;
    border-radius: 3px;
    display: flex;
    width: 100%;
    height: 100%;
    justify-content: space-around;
    align-items: stretch;
    flex-direction: row; */
    /* position: absolute; */
    /* bottom: 1%; */
    /* left: 50%; */
    /* transform: translateX(-50%); */
    /* display: flex; */
    /* gap: 10px; */
    /* z-index: 1000; */
    /* height: 6%; */
    /* margin-left: auto; */
    /* margin-right: auto; */
    /* width: 50%; */
    /* display: flex;
    justify-content: center; */
    }

.switch-button {
    background-color: rgb(0 0 0 / 56%);
    color: white;
    border: none;
    padding: 10px 20px;
    /* border-radius: 5px; */
    cursor: pointer;
    transition: background-color 0.3s;
    text-shadow: black 2px 2px 2px;
    font-size: 16px;
}

.switch-button.active {
    background-color: rgba(0, 0, 0, 0.8);
}

.switch-button:hover {
    background-color: rgba(0, 0, 0, 0.8);
}

.character {
    width: calc(var(--grid-cell)* 2);
    height: calc(var(--grid-cell)* 2);
    position: absolute;
    overflow: hidden;
}

.shadow {
    width: calc(var(--grid-cell)* 2);
    height: calc(var(--grid-cell)* 2);
    position: absolute;
    left: 0;
    top: 0;
    background: url("https://assets.codepen.io/21542/DemoRpgCharacterShadow.png") no-repeat no-repeat;
    background-size: 100%;
}

.character_spritesheet {
    position: absolute;
    background: url("https://assets.codepen.io/21542/DemoRpgCharacter.png") no-repeat no-repeat;
    background-size: 100%;
    width: calc(var(--grid-cell)* 8);
    height: calc(var(--grid-cell)* 8);
}

.character[facing="right"] .character_spritesheet {
    background-position-y: calc(var(--pixel-size) * -32);
}

.character[facing="up"] .character_spritesheet {
    background-position-y: calc(var(--pixel-size) * -64);
}

.character[facing="left"] .character_spritesheet {
    background-position-y: calc(var(--pixel-size) * -96);
}

.character[walking="true"] .character_spritesheet {
    animation: walkAnimation 0.6s steps(4) infinite;
}

@keyframes walkAnimation {
    from {
        transform: translate3d(0%, 0%, 0);
    }

    to {
        transform: translate3d(-100%, 0%, 0);
    }
}

.map {
    image-rendering: pixelated;
    background-image: url("https://assets.codepen.io/21542/CameraDemoMap.png");
    background-size: 100%;
    width: calc(13 * var(--grid-cell));
    height: calc(10 * var(--grid-cell));
    position: relative;
}

.game-screen {
    width: calc(var(--pixel-size) * 160);
    height: calc(var(--pixel-size) * 144);
    overflow: hidden;
    background: #61ddf7;
    position: relative;
}

.pixel-art {
    image-rendering: pixelated;
}

canvas {
    display: block;
}

/* // .action-buttons-container {
//     position: fixed;
//     bottom: 20px;
//     right: 20px;
//     display: flex;
//     flex-direction: column;
//     gap: 10px;
//     z-index: 900;
// }

// .action-button {
//     padding: 10px 15px;
//     border-radius: 5px;
//     font-weight: bold;
//     cursor: pointer;
//     transition: all 0.2s ease;
//     border: 1px solid rgba(255, 255, 255, 0.3);
//     box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
// } */

#season-weather-display {
    display: flex;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    justify-content: space-around;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding: 6px 0;
}

#weatherDisplay {
    color: white;
    font-size: 14px;
    text-shadow: 2px 2px 2px black;
    padding: 1px 0;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    /* border-top: 1px solid rgba(255, 255, 255, 0.2); */
}

.weather-display {
    color: white;
    font-size: 14px;
    text-shadow: 2px 2px 2px black;
    padding: 3px 0;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

/* progressbar overlay styles */
#progress-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
}

.progress-container {
    background: rgba(0, 0, 0, 0.8);
    border-radius: 10px;
    padding: 20px;
    width: 300px;
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.2);
}

.progress-text {
    color: white;
    font-size: 18px;
    text-align: center;
    margin-bottom: 15px;
    text-shadow: 2px 2px 2px black;
}

.progress-bar-container {
    background: rgba(0, 0, 0, 0.5);
    height: 20px;
    border-radius: 10px;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

#progress-bar {
    height: 100%;
    width: 0%;
    background: linear-gradient(to right, #4CAF50, #8BC34A);
    transition: width 0.03s ease-in-out;
    border-radius: 10px;
}

#settingsButton {
    position: fixed;
    bottom: 10px;
    right: 10px;
    z-index: 1000;
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 10%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 20px;
    /* filter: invert(1); */
    /* text-align: center; */
}

#statusContainer {
    /* position: fixed; */
    /* top: 18px; */
    /* left: 26px; */
    /* color: white; */
    /* font-size: 14px; */
    /* text-shadow: 2px 2px 2px black; */
    z-index: 9;
    /* background: rgba(40, 40, 40, 0.6); */
    /* padding: 10px 9px 0px 5px; */
    /* border-radius: 5px; */
    /* width: 180px; */
}

/* Time display styles moved to time-display.css */

.dark {
    --background: oklch(0.13 0.028 261.692);
    --foreground: oklch(0.985 0.002 247.839);
    --card: oklch(0.21 0.034 264.665);
    --card-foreground: oklch(0.985 0.002 247.839);
    --popover: oklch(0.21 0.034 264.665);
    --popover-foreground: oklch(0.985 0.002 247.839);
    --primary: oklch(0.928 0.006 264.531);
    --primary-foreground: oklch(0.21 0.034 264.665);
    --secondary: oklch(0.278 0.033 256.848);
    --secondary-foreground: oklch(0.985 0.002 247.839);
    --muted: oklch(0.278 0.033 256.848);
    --muted-foreground: oklch(0.707 0.022 261.325);
    --accent: oklch(0.278 0.033 256.848);
    --accent-foreground: oklch(0.985 0.002 247.839);
    --destructive: oklch(0.704 0.191 22.216);
    --border: oklch(1 0 0 / 10%);
    --input: oklch(1 0 0 / 15%);
    --ring: oklch(0.551 0.027 264.364);
    --chart-1: oklch(0.488 0.243 264.376);
    --chart-2: oklch(0.696 0.17 162.48);
    --chart-3: oklch(0.769 0.188 70.08);
    --chart-4: oklch(0.627 0.265 303.9);
    --chart-5: oklch(0.645 0.246 16.439);
    --sidebar: oklch(0.21 0.034 264.665);
    --sidebar-foreground: oklch(0.985 0.002 247.839);
    --sidebar-primary: oklch(0.488 0.243 264.376);
    --sidebar-primary-foreground: oklch(0.985 0.002 247.839);
    --sidebar-accent: oklch(0.278 0.033 256.848);
    --sidebar-accent-foreground: oklch(0.985 0.002 247.839);
    --sidebar-border: oklch(1 0 0 / 10%);
    --sidebar-ring: oklch(0.551 0.027 264.364);
}

@theme inline {
    --radius-sm: calc(var(--radius) - 4px);
    --radius-md: calc(var(--radius) - 2px);
    --radius-lg: var(--radius);
    --radius-xl: calc(var(--radius) + 4px);
    --color-background: var(--background);
    --color-foreground: var(--foreground);
    --color-card: var(--card);
    --color-card-foreground: var(--card-foreground);
    --color-popover: var(--popover);
    --color-popover-foreground: var(--popover-foreground);
    --color-primary: var(--primary);
    --color-primary-foreground: var(--primary-foreground);
    --color-secondary: var(--secondary);
    --color-secondary-foreground: var(--secondary-foreground);
    --color-muted: var(--muted);
    --color-muted-foreground: var(--muted-foreground);
    --color-accent: var(--accent);
    --color-accent-foreground: var(--accent-foreground);
    --color-destructive: var(--destructive);
    --color-border: var(--border);
    --color-input: var(--input);
    --color-ring: var(--ring);
    --color-chart-1: var(--chart-1);
    --color-chart-2: var(--chart-2);
    --color-chart-3: var(--chart-3);
    --color-chart-4: var(--chart-4);
    --color-chart-5: var(--chart-5);
    --color-sidebar: var(--sidebar);
    --color-sidebar-foreground: var(--sidebar-foreground);
    --color-sidebar-primary: var(--sidebar-primary);
    --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
    --color-sidebar-accent: var(--sidebar-accent);
    --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
    --color-sidebar-border: var(--sidebar-border);
    --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
    }
  body {
    @apply bg-background text-foreground;
    }
}


#moveButton {
    position: fixed;
    bottom: 31px;
    left: 31px;
    padding: 10px 10px;
    font-size: 16px;
    background-color: rgba(0, 0, 0, 0.58);
    color: white;
    border: 1px solid rgb(145, 145, 145);
    display: none;
    z-index: 1000;
}

/* Status bars styles */
.status-bars {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.ui-panel {
    background: rgb(16 16 16);
    padding: 11px;
    border-radius: 3px;
    backdrop-filter: blur(5px);
}

.status-bar-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 4px;
}

.status-icon {
    font-size: 16px;
    width: 20px;
    text-align: center;
    flex-shrink: 0;
}

.status-bar-wrapper {
    flex-grow: 1;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 3px;
    height: 20px;
    position: relative;
    min-width: 140px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.status-bar {
    width: 100%;
    height: 100%;
    border-radius: 3px;
    transition: width 0.3s ease;
    position: relative;
    overflow: hidden;
    background-color: white;
}

.status-icon-img {
    width: 70%;
    height: auto;
    filter:invert(0.8);
}

.health-bar {
    /* background: linear-gradient(90deg, #FF5252 0%, #FF8A80 100%); */
    background: linear-gradient(90deg, #ff0000 0%, #ff3c2b 100%);
    box-shadow: 0 0 10px rgba(255, 82, 82, 0.3);
}

.food-bar {
    /* background: linear-gradient(90deg, #4CAF50 0%, #81C784 100%); */
    background: linear-gradient(90deg, #008505 0%, #00d309 100%);
    box-shadow: 0 0 10px rgba(76, 175, 80, 0.3);
}

.water-bar {
    /* background: linear-gradient(90deg, #2196F3 0%, #64B5F6 100%); */
    background: linear-gradient(90deg, #0c94ff 0%, #008dff 100%);
    box-shadow: 0 0 10px rgba(33, 150, 243, 0.3);
}

.energy-bar {
    /* background: linear-gradient(90deg, #FFC107 0%, #FFD54F 100%); */
    background: linear-gradient(90deg, #ffbf00 0%, #ffc612 100%);
    box-shadow: 0 0 10px rgba(255, 193, 7, 0.3);
}

/* Animation for fadeOut referenced in prompt-overlay */
@keyframes fadeOut {
    0% { opacity: 1; }
    70% { opacity: 1; }
    100% { opacity: 0; }
}

/* Responsive design for status bars */
@media (max-width: 768px) {
    .status-bars {
        top: 10px;
        left: 10px;
        padding: 10px;
        gap: 8px;
    }

    .status-bar-wrapper {
        min-width: 100px;
        height: 18px;
    }

    .status-icon {
        font-size: 14px;
        width: 18px;
    }

    .status-bar-item {
        margin-bottom: 8px;
        gap: 8px;
    }
}

@media (max-width: 480px) {
    .status-bars {
        top: 5px;
        left: 5px;
        padding: 8px;
        gap: 6px;
    }

    .status-bar-wrapper {
        min-width: 80px;
        height: 16px;
    }

    .status-icon {
        font-size: 12px;
        width: 16px;
    }

    .status-bar-item {
        margin-bottom: 6px;
        gap: 6px;
    }
}
