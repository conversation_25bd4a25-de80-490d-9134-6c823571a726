import React = require("react");
import { InventoryItemStack, Item } from "src/Interfaces";
import { Items } from "src/enums/resources";
import {
  DndContext,
  useSensor,
  useSensors,
  MouseSensor,
  TouchSensor,
  DragOverlay,
  DragStartEvent,
  DragEndEvent,
  CollisionDetection,
  rectIntersection,
} from "@dnd-kit/core";
import { snapCenterToCursor } from '@dnd-kit/modifiers';
import { useRootStore } from "../stores/rootStore";
import { ItemsGrid } from "./Inventory/ItemsGrid";
import { InventoryItem } from "./Inventory/InventoryItem";
import { ItemDescriptionPopover } from "./ItemDescriptionPopover";


const fixCursorSnapOffset: CollisionDetection = (args) => {
    // Bail out if keyboard activated
    if (!args.pointerCoordinates) {
      return rectIntersection(args);
    }
    const { x, y } = args.pointerCoordinates;
    const { width, height } = args.collisionRect;
    const updated = {
      ...args,
      // The collision rectangle is broken when using snapCenterToCursor. Reset
      // the collision rectangle based on pointer location and overlay size.
      collisionRect: {
        width,
        height,
        bottom: y + height / 2,
        left: x - width / 2,
        right: x + width / 2,
        top: y - height / 2,
      },
    };
    return rectIntersection(updated);
};



export const InventoryPanel = () => {
    // Use our custom hook with shallow comparison to prevent unnecessary re-renders
    // const itemStacks = useStoreShallow(useRootStore, state => state.itemStacks);
    const itemStacks = useRootStore(state => state.itemStacks);
    const setItemStacks = useRootStore(state => state.setItemStacks);

    const [selectedStack, setSelectedStack] = React.useState<InventoryItemStack>(null);
    const [activeId, setActiveId] = React.useState(null);
    const [activeIndex, setActiveIndex] = React.useState(null);
    const [selectedItemRect, setSelectedItemRect] = React.useState<DOMRect | null>(null);

    // Function to handle item selection/deselection
    const handleItemSelection = React.useCallback((itemStack: InventoryItemStack, element?: HTMLElement) => {
        // Store the bounding rectangle of the selected item for popover positioning
        if (element) {
            // Get the bounding rectangle relative to the viewport
            const rect = element.getBoundingClientRect();
            console.log("Selected item rect:", rect);

            // Set the rect first, then the selected stack to ensure the popover has the position
            // before it tries to render
            setSelectedItemRect(rect);

            // Use a small timeout to ensure the rect is set before the popover renders
            setTimeout(() => {
                setSelectedStack(itemStack);
            }, 0);
        } else {
            console.warn("No element reference provided for popover positioning");
            setSelectedStack(itemStack);
        }
    }, [selectedStack]);


    // Configure sensors for drag detection
    // Don't use useMemo for these as they're causing dependency array issues
    const mouseSensor = useSensor(MouseSensor, {
        // Require the mouse to move by 10 pixels before activating
        activationConstraint: {
            distance: 10,
        },
    });

    const touchSensor = useSensor(TouchSensor, {
        // Press delay of 250ms, with tolerance of 5px of movement
        activationConstraint: {
            delay: 250,
            tolerance: 5,
        },
    });

    // Create sensors without memoization to avoid dependency array issues
    const sensors = useSensors(mouseSensor, touchSensor);

    // Memoize event handlers to prevent re-renders
    const handleDragStart = React.useCallback((event: DragStartEvent) => {
        const { active } = event;
        const { index } = active.data.current;
        setActiveId(active.id);
        setActiveIndex(index);
    }, []);

    const handleDragEnd = React.useCallback((event: DragEndEvent) => {
        const { active, over } = event;

        if (over && active.id !== over.id) {
            const fromIndex = active.data.current.index;
            const toIndex = over.data.current.index;


            // Swap the items
            const newItemStacks = [...itemStacks];
            const temp = newItemStacks[fromIndex];
            newItemStacks[fromIndex] = newItemStacks[toIndex];
            newItemStacks[toIndex] = temp;
            console.log("Swapping", fromIndex, "to", toIndex, itemStacks, newItemStacks);
            setItemStacks(newItemStacks);
        }

        setActiveId(null);
        setActiveIndex(null);
    }, [itemStacks, setItemStacks, setActiveId, setActiveIndex]);

    // Memoize these values to prevent re-renders
    // const itemDef = React.useMemo(() => selectedStack ? Items[selectedStack.itemId] : null, [selectedStack]);

    // Calculate activeItem as a value, not a function
    const activeItem = activeIndex !== null ? itemStacks[activeIndex] : null;
    const activeItemDef = activeItem ? Items[activeItem.itemId] : null;

    return (
        <DndContext
            sensors={sensors}
            onDragStart={handleDragStart}
            onDragEnd={handleDragEnd}
            collisionDetection={fixCursorSnapOffset}
        >
            <div id="inventoryContent">
                {/* <h3 className="inventory-container">{getText("Inventory")}:</h3>
                <div className="inventory-count">
                    {itemStacks.length}/{props.inventorySize} slots
                </div> */}
                <ItemsGrid
                    itemStacks={itemStacks}
                    selectedStack={selectedStack}
                    setSelectedStack={handleItemSelection}
                    activeId={activeId}
                />

                {/* Render the popover when an item is selected */}
                {selectedStack && (
                    <ItemDescriptionPopover
                        selectedStack={selectedStack}
                        // itemDef={itemDef}
                        anchorRect={selectedItemRect}
                        isOpen={!!selectedStack}
                        onClose={() => {
                            setSelectedStack(null);
                            setSelectedItemRect(null);
                        }}
                    />
                )}
            </div>

            <DragOverlay modifiers={[snapCenterToCursor]} dropAnimation={null} >
                {activeItem && activeItemDef && (
                    <div className="inventory-item-overlay">
                        {/* <div className="inventory-item-inner"> */}
                        <div className="drag-overlay">
                            <InventoryItem
                                itemStack={activeItem}
                                itemDef={activeItemDef}
                            />
                        </div>
                    </div>
                )}
            </DragOverlay>
        </DndContext>
    )
};

