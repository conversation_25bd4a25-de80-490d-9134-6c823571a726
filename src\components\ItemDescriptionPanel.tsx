import React = require("react");
import { ResourceTypes, statsIconMap } from "src/enums/common_enum";
import { ItemIcon } from "./common";
import { useEquipmentStore } from "src/stores/equipmentStore";
import { useRootStore } from "src/stores/rootStore";
import { getText } from "src/i18n";
import { startProgressBar } from "src/gameinfo";
import { InventoryItemStack } from "src/Interfaces";


function getSignFromNumber(num: number): string {
    return num >= 0 ? '+' : '-';
}

interface StatLineProps {
    stat: number;
    type: string;
}

const StatLine: React.FC<StatLineProps> = ({stat, type}) => {
    if (!stat) {
        return null;
    }

    return (
        <>
            {statsIconMap[type]} Food: {getSignFromNumber(stat)} {stat}
            <br/>
        </>
    );
};

interface ItemDescriptionPanelProps {
    selectedStack: InventoryItemStack;
    onClose?: () => void;
    showActions?: boolean;
}

export const ItemDescriptionPanel: React.FC<ItemDescriptionPanelProps> = ({selectedStack, onClose, showActions = false}) => {
    const equipItem = useEquipmentStore(state => state.equipItem);
    const itemStacks = useRootStore(state => state.itemStacks);
    const removeItemsFromInventory = useRootStore(state => state.removeItemsFromInventory);

    const itemDef = selectedStack.itemDef;

    // Handle equip button click
    const handleEquip = () => {
        if (selectedStack.itemDef.type.id === 'Equipable' && selectedStack.itemDef.slotType) {
            // Find the item in inventory
            const itemStack = itemStacks.find(stack =>
                stack && stack.itemId === itemDef.id
            );

            if (itemStack) {
                equipItem(itemStack, itemDef.slotType.id);
                if (onClose) onClose();
            }
        }
    };

    // Handle eat button click
    const handleEat = () => {
        // Implement eat functionality here
        console.log("Eating", selectedStack.itemDef.name);
        startProgressBar(getText("Eating..."), 5, () => {
            removeItemsFromInventory([{itemDef: selectedStack.itemDef, uuid: selectedStack.uuid, quantity: 1}]);
        })
        if (onClose) onClose();
    };

    return (
        <>
            <div className="selected-resource-header">
                <ItemIcon itemDef={itemDef} />
                <span className="resource-name">{itemDef.name}</span>
            </div>
            <div className="resource-type">
                {itemDef.type.name} {itemDef.type.icon}
            </div>
            <div className="resource-stats">
                {itemDef.description || 'No description available'}
            </div>

            {/* Show equipment slot type if it's an equipable item */}
            {itemDef.type.id === 'Equipable' && itemDef.slotType && (
                <div className="equipment-info">
                    <div className="slot-type">
                        <span className="slot-icon">{itemDef.slotType.icon}</span>
                        <span className="slot-name">{getText("Slot")}: {itemDef.slotType.name}</span>
                    </div>
                </div>
            )}

            {/* Show food stats if it's an edible item */}
            {(itemDef.type === ResourceTypes.EDIBLE || itemDef.type === ResourceTypes.MEDICINAL) &&
                <div>
                    <StatLine stat={itemDef.food} type="food" />
                    <StatLine stat={itemDef.water} type="water" />
                    <StatLine stat={itemDef.energy} type="energy" />
                    <StatLine stat={itemDef.health} type="health" />
                </div>
            }

            {/* Action buttons */}
            {showActions && (
                <div className="item-actions">
                    {/* Equip button for equipable items */}
                    {itemDef.type.id === 'Equipable' && (
                        <button className="equip-button" onClick={handleEquip}>
                            {getText("Equip")}
                        </button>
                    )}

                    {/* Eat button for food items */}
                    {itemDef.type === ResourceTypes.EDIBLE && (
                        <button className="green-btn" onClick={handleEat}>
                            {getText("Eat")}
                        </button>
                    )}
                </div>
            )}
        </>
    );
};

