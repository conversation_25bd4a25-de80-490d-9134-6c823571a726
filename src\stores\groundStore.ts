import { InventoryItemStack } from "src/Interfaces";
import { mapSize } from "src/settings";
import { create } from "zustand";

interface GroundStore {
    allRegionGroundStacks: InventoryItemStack[][];
    setGroundStacks: (itemStacks: InventoryItemStack[]) => void;
    addToGroundStacks: (regionIndex: number, itemStacks: InventoryItemStack[]) => void;
    removeStackByIndex: (regionIndex: number, stackIndex: number) => void;
    swapStacks: (regionIndex: number, fromIndex: number, toIndex: number) => void;
    addStackByIndex: (regionIndex: number, stackIndex: number, itemStack: InventoryItemStack) => void;
}

export const useGroundStore = create<GroundStore>((set, get) => ({
    allRegionGroundStacks: new Array(mapSize['medium']).fill(null).map(() => new Array(5)),
    setGroundStacks:  (itemStacks: InventoryItemStack[]) => {
        
    },
    // initializeRegion: (regionIndex) => set((state) => {
    //     const allRegionGroundStacks = [];
    //     return { allRegionGroundStacks: newPlacedBuildingsInAllRegions };
    // }),

    addToGroundStacks: (regionIndex: number, itemStacks: InventoryItemStack[]) => set((state) => {
        const newAllRegionGroundStacks = [...state.allRegionGroundStacks];
        const groundStacks = newAllRegionGroundStacks[regionIndex];
        if (groundStacks == null) {
            newAllRegionGroundStacks[regionIndex] = [...itemStacks];
        } else {
            newAllRegionGroundStacks[regionIndex] = [...groundStacks, ...itemStacks];
        }
        return { allRegionGroundStacks: newAllRegionGroundStacks };
    }),

    addStackByIndex: (regionIndex: number, stackIndex: number, itemStack: InventoryItemStack) => set((state) => {
        
        const newAllRegionGroundStacks = [...state.allRegionGroundStacks];
        const newGroundStacks = [...newAllRegionGroundStacks[regionIndex]];
        newAllRegionGroundStacks[regionIndex] = newGroundStacks.splice(stackIndex, 1, itemStack);
        
        return { allRegionGroundStacks: newAllRegionGroundStacks };
    }),

    removeStackByIndex: (regionIndex: number, stackIndex: number) => set((state) => {
        const newAllRegionGroundStacks = [...state.allRegionGroundStacks];
        const newGroundStacks = [...newAllRegionGroundStacks[regionIndex]];
        newAllRegionGroundStacks[regionIndex] = newGroundStacks.splice(stackIndex, 1);
        
        return { allRegionGroundStacks: newAllRegionGroundStacks };
    }),

    swapStacks: (regionIndex: number, fromIndex: number, toIndex: number) => set((state) => {
        const newAllRegionGroundStacks = [...state.allRegionGroundStacks];
        const newGroundStacks = [...newAllRegionGroundStacks[regionIndex]];
        const temp = newGroundStacks[fromIndex];
        newGroundStacks[fromIndex] = newGroundStacks[toIndex];
        newGroundStacks[toIndex] = temp;
        newAllRegionGroundStacks[regionIndex] = newGroundStacks;
        
        return { allRegionGroundStacks: newAllRegionGroundStacks };
    }),

    // tryConsumeItemsFromGroundStacks: (regionIndex: number, itemsToRemove: ItemToConsume[]) => {
    //     let success = false;
        
    //     set((state) => {
    //         const newAllRegionGroundStacks = [...state.allRegionGroundStacks];
    //         const groundStacks = newAllRegionGroundStacks[regionIndex];
    //         if (groundStacks == null) {
    //             return { allRegionGroundStacks: newAllRegionGroundStacks };
    //         } else {
    //             const newGroundStacks = groundStacks.filter(stack => !itemStacks.find(itemStack => itemStack.uuid === stack.uuid));
    //             newAllRegionGroundStacks[regionIndex] = newGroundStacks;
    //             return { allRegionGroundStacks: newAllRegionGroundStacks };
    //         }
    //     })
    // },
}));
