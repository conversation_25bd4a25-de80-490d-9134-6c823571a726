import SimplexNoise from 'simplex-noise';
import { makeRandFloat } from '@redblobgames/prng';

export class FogOfWar {
    // Grid-based exploration tracking for O(1) lookups
    // Store exploredGrid for loading saved games
    private exploredGrid: boolean[][];
    private gridSize: number = 2; // Size of each grid cell in world units
    private gridWidth: number;
    private gridHeight: number;

    // Canvas for rendering
    private fogCanvas: HTMLCanvasElement;
    private fogCtx: CanvasRenderingContext2D;

    // World dimensions
    private worldWidth: number;
    private worldHeight: number;
    private visibilityRadius: number = 50; // Base visibility radius

    // Visual effects for smooth boundaries
    private noise: any;
    private noiseScale: number = 0.02;
    private noiseAmplitude: number = 15;

    // Performance optimization
    private lastPlayerGridX: number = -1;
    private lastPlayerGridY: number = -1;
    private radiusInGridCells: number = 0;
    
    constructor(worldWidth: number, worldHeight: number) {
        this.worldWidth = worldWidth;
        this.worldHeight = worldHeight;

        // Initialize grid system for fast exploration lookups
        this.gridWidth = Math.ceil(worldWidth / this.gridSize);
        this.gridHeight = Math.ceil(worldHeight / this.gridSize);
        this.exploredGrid = Array(this.gridHeight).fill(null).map(() => Array(this.gridWidth).fill(false));
        this.radiusInGridCells = Math.ceil(this.visibilityRadius / this.gridSize);
        console.log("gridWidth", this.gridWidth);

        // Create off-screen canvas for fog rendering
        this.fogCanvas = document.createElement('canvas');
        this.fogCanvas.width = worldWidth;
        this.fogCanvas.height = worldHeight;
        this.fogCtx = this.fogCanvas.getContext('2d')!;

        // Initialize noise for irregular boundaries
        this.noise = new (SimplexNoise as any)(makeRandFloat(12345));

        // Initialize with full fog
        this.clearFog();
    }
    
    /**
     * Clear the fog canvas and fill with black fog
     */
    private clearFog(): void {
        // Use slightly transparent fog for better visual blending
        this.fogCtx.fillStyle = 'rgba(0, 0, 0, 0.9)';
        this.fogCtx.fillRect(0, 0, this.worldWidth, this.worldHeight);
    }
    
    /**
     * Update explored areas based on player position
     */
    public updateExploration(playerX: number, playerY: number, needsFullRedraw: boolean = false): void {
        // Convert world coordinates to grid coordinates
        const gridX = Math.floor(playerX / this.gridSize);
        const gridY = Math.floor(playerY / this.gridSize);

        // No need to update if current grid cell has already been explored
        let hasNewExploration = false;
        if (this.exploredGrid[gridY][gridX]) {
            return; 
        } else {
            this.exploredGrid[gridY][gridX] = true;
            hasNewExploration = true;
            // console.log("hasNewExploration", playerX, playerY, gridX, gridY);
        }



        // // Check if player moved to a new grid cell
        // if (gridX === this.lastPlayerGridX && gridY === this.lastPlayerGridY) {
        //     return; // No need to update if still in same grid cell
        // }

        // this.lastPlayerGridX = gridX;
        // this.lastPlayerGridY = gridY;

        // // For incremental updates, we don't need to store all areas
        // // We'll only add to the array when doing full redraws
        // // Mark exploration area around player in grid for fast lookups
        // let hasNewExploration = false;
        // for (let dy = -this.radiusInGridCells; dy <= this.radiusInGridCells; dy++) {
        //     for (let dx = -this.radiusInGridCells; dx <= this.radiusInGridCells; dx++) {
        //         const gx = gridX + dx;
        //         const gy = gridY + dy;

        //         // Check bounds
        //         if (gx < 0 || gx >= this.gridWidth || gy < 0 || gy >= this.gridHeight) {
        //             continue;
        //         }

        //         // Check if within circular radius
        //         const distance = Math.sqrt(dx * dx + dy * dy) * this.gridSize;
        //         if (distance <= this.visibilityRadius) {
        //             if (!this.exploredGrid[gy][gx]) {
        //                 this.exploredGrid[gy][gx] = true;
        //                 hasNewExploration = true;
        //             }
        //         }
        //     }
        // }



        // // Log exploration progress
        // const stats = this.getExplorationStats();
        // console.log(`Fog of War: ${stats.exploredCells}/${stats.totalCells} cells explored (${stats.explorationPercentage.toFixed(1)}%)`);


        if (needsFullRedraw) {
            // Full redraw needed (world size change, reset, etc.)
            this.regenerateFogFull();
        } else if (hasNewExploration) {
            // Only redraw if there was new exploration
            // Incremental update - only draw the new explored area
            this.drawNewExploredArea({
                x: playerX,
                y: playerY,
                radius: this.visibilityRadius
            });
        }
    }
    
    /**
     * Regenerate the fog based on explored grid (full redraw)
     */
    private regenerateFogFull(): void {
        console.time("regenerateFogFull");

        // Clear and fill with fog
        this.clearFog();

        // Use destination-out to "cut holes" in the fog for explored areas
        this.fogCtx.globalCompositeOperation = 'destination-out';

        // Draw explored areas based on grid - create circles for each explored grid cell
        for (let gy = 0; gy < this.gridHeight; gy++) {
            for (let gx = 0; gx < this.gridWidth; gx++) {
                if (this.exploredGrid[gy][gx]) {
                    // Create a circle at the center of each explored grid cell
                    const centerX = (gx + 0.5) * this.gridSize;
                    const centerY = (gy + 0.5) * this.gridSize;
                    this.drawExploredArea({
                        x: centerX,
                        y: centerY,
                        radius: this.visibilityRadius * 0.8 // Slightly smaller to avoid over-overlap
                    });
                }
            }
        }

        // Reset composite operation
        this.fogCtx.globalCompositeOperation = 'source-over';

        console.timeEnd("regenerateFogFull");
    }

    /**
     * Incrementally add a new explored area to the fog (much faster)
     */
    private drawNewExploredArea(area: {x: number, y: number, radius: number}): void {
        // console.time("drawNewExploredArea");

        // Use destination-out to "cut a hole" in the fog for this new area
        this.fogCtx.globalCompositeOperation = 'destination-out';

        // Draw only the new explored area
        this.drawExploredArea(area);

        // Reset composite operation
        this.fogCtx.globalCompositeOperation = 'source-over';

        // console.timeEnd("drawNewExploredArea");
    }

    /**
     * Draw an explored area with irregular, blurred boundaries
     */
    private drawExploredArea(area: {x: number, y: number, radius: number}): void {
        const centerX = area.x;
        const centerY = area.y;
        const baseRadius = area.radius;

        // Create a gradient for smooth falloff
        const gradient = this.fogCtx.createRadialGradient(
            centerX, centerY, 0,
            centerX, centerY, baseRadius * 1.2
        );
        gradient.addColorStop(0, 'rgba(255, 255, 255, 1)'); // Fully revealed at center
        gradient.addColorStop(0.7, 'rgba(255, 255, 255, 0.8)'); // Mostly revealed
        gradient.addColorStop(1, 'rgba(255, 255, 255, 0)'); // Fade to fog at edges

        this.fogCtx.fillStyle = gradient;

        // Draw irregular circle using noise for boundary variation
        this.fogCtx.beginPath();

        const numPoints = 32; // Reduced from 64 for better performance
        let firstPoint = true;

        for (let i = 0; i <= numPoints; i++) {
            const angle = (i / numPoints) * Math.PI * 2;

            // Get noise value for this angle to create irregular boundary
            const noiseX = centerX + Math.cos(angle) * baseRadius * 0.5;
            const noiseY = centerY + Math.sin(angle) * baseRadius * 0.5;
            const noiseValue = this.noise.noise2D(noiseX * this.noiseScale, noiseY * this.noiseScale);

            // Apply noise to radius for irregular boundary
            const irregularRadius = baseRadius + (noiseValue * this.noiseAmplitude);

            const x = centerX + Math.cos(angle) * irregularRadius;
            const y = centerY + Math.sin(angle) * irregularRadius;

            if (firstPoint) {
                this.fogCtx.moveTo(x, y);
                firstPoint = false;
            } else {
                this.fogCtx.lineTo(x, y);
            }
        }

        this.fogCtx.closePath();
        this.fogCtx.fill();
    }
    

    
    /**
     * Render the fog overlay on the main canvas
     */
    public renderFog(ctx: CanvasRenderingContext2D, cameraX: number, cameraY: number): void {
        ctx.save();

        // Use source-over for better gradient blending with irregular boundaries
        ctx.globalCompositeOperation = 'source-over';
        ctx.globalAlpha = 1.0;

        // Draw the fog canvas
        ctx.drawImage(this.fogCanvas, -cameraX, -cameraY);

        ctx.restore();
    }

    /**
     * Get the fog canvas for debugging or other purposes
     */
    public getFogCanvas(): HTMLCanvasElement {
        return this.fogCanvas;
    }
    
    /**
     * Update world size if needed
     */
    public updateWorldSize(width: number, height: number): void {
        if (this.worldWidth !== width || this.worldHeight !== height) {
            this.worldWidth = width;
            this.worldHeight = height;

            // Recalculate grid dimensions
            const newGridWidth = Math.ceil(width / this.gridSize);
            const newGridHeight = Math.ceil(height / this.gridSize);

            // Resize exploration grid if needed
            if (newGridWidth !== this.gridWidth || newGridHeight !== this.gridHeight) {
                this.gridWidth = newGridWidth;
                this.gridHeight = newGridHeight;
                this.exploredGrid = Array(this.gridHeight).fill(null).map(() => Array(this.gridWidth).fill(false));
            }

            this.fogCanvas.width = width;
            this.fogCanvas.height = height;
            // this.regenerateFogFull();
        }
    }
    
    /**
     * Set visibility radius
     */
    public setVisibilityRadius(radius: number): void {
        this.visibilityRadius = radius;
    }

    /**
     * Get exploration statistics for debugging
     */
    public getExplorationStats(): { exploredCells: number, totalCells: number, explorationPercentage: number } {
        let exploredCells = 0;
        for (let gy = 0; gy < this.gridHeight; gy++) {
            for (let gx = 0; gx < this.gridWidth; gx++) {
                if (this.exploredGrid[gy][gx]) {
                    exploredCells++;
                }
            }
        }

        const totalCells = this.gridWidth * this.gridHeight;
        const explorationPercentage = (exploredCells / totalCells) * 100;

        return { exploredCells, totalCells, explorationPercentage };
    }
    
    // /**
    //  * Clear all exploration (reset fog of war)
    //  */
    // public resetExploration(): void {
    //     // Reset the exploration grid
    //     for (let gy = 0; gy < this.gridHeight; gy++) {
    //         for (let gx = 0; gx < this.gridWidth; gx++) {
    //             this.exploredGrid[gy][gx] = false;
    //         }
    //     }

    //     // Reset tracking variables
    //     this.lastPlayerGridX = -1;
    //     this.lastPlayerGridY = -1;

    //     this.clearFog();
    // }
}
